<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { tenantAdd, tenantInfo, tenantUpdate } from '#/api/system/tenant';
import { packageSelectList } from '#/api/system/tenant-package';
import { useTenantStore } from '#/store/tenant';

import { drawerSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

// 处理租户logo文件上传
const logoFileList = ref<any[]>([]);
const logoFile = ref<File | null>(null);

// 处理租户首页logo文件上传
const homeLogoFileList = ref<any[]>([]);
const homeLogoFile = ref<File | null>(null);

// 处理租户移动端logo文件上传
const mobileLogoFileList = ref<any[]>([]);
const mobileLogoFile = ref<File | null>(null);

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    formItemClass: 'col-span-2',
    labelWidth: 100,
    componentProps: {
      class: 'w-full',
    },
  },
  schema: drawerSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }

    drawerApi.drawerLoading(true);

    try {
      const { id } = drawerApi.getData() as { id?: number | string };
      isUpdate.value = !!id;

      // 1. 初始化套餐选择器
      await setupPackageSelect();

      // 2. 配置表单组件
      await updateFormComponents();

      // 3. 编辑模式: 获取租户数据并设置表单值
      if (isUpdate.value && id) {
        const record = await tenantInfo(id);

        // 设置表单值
        await formApi.setValues(record);

        // 4. 先设置logo预览（如果有logo）
        if (record && record.tenantLogo) {
          await setupLogoPreview('tenantLogo', record.tenantLogo);
        }
        if (record && record.homeLogo) {
          await setupLogoPreview('homeLogo', record.homeLogo);
        }
        if (record && record.mobileLogo) {
          await setupLogoPreview('mobileLogo', record.mobileLogo);
        }
        // 5. 等待一下确保所有更新完成
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    } catch (error) {
      console.error('初始化表单错误:', error);
    } finally {
      drawerApi.drawerLoading(false);
    }
  },
});

// 更新表单组件配置
async function updateFormComponents() {
  // 设置套餐是否可用
  formApi.updateSchema([
    {
      fieldName: 'packageId',
      componentProps: {
        disabled: isUpdate.value,
      },
    },
    {
      fieldName: 'tenantLogoUpload',
      componentProps: {
        maxCount: 1,
        multiple: false,
        listType: 'picture-card',
        accept: 'image/*',
        showUploadList: true,
        beforeUpload: () => false, // 阻止自动上传
        onChange: (info: any) => {
          handleLogoChange('tenantLogo', info);
        },
        fileList: logoFileList.value,
      },
    },
    {
      fieldName: 'homeLogoUpload',
      componentProps: {
        maxCount: 1,
        multiple: false,
        listType: 'picture-card',
        accept: 'image/*',
        showUploadList: true,
        beforeUpload: () => false, // 阻止自动上传
        onChange: (info: any) => {
          handleLogoChange('homeLogo', info);
        },
        fileList: homeLogoFileList.value,
      },
    },
    {
      fieldName: 'mobileLogoUpload',
      componentProps: {
        maxCount: 1,
        multiple: false,
        listType: 'picture-card',
        accept: 'image/*',
        showUploadList: true,
        beforeUpload: () => false, // 阻止自动上传
        onChange: (info: any) => {
          handleLogoChange('mobileLogo', info);
        },
        fileList: mobileLogoFileList.value,
      },
    },
  ]);
}

// 处理Logo文件变更
function handleLogoChange(
  logoType: 'homeLogo' | 'mobileLogo' | 'tenantLogo',
  info: any,
) {
  switch (logoType) {
    case 'homeLogo': {
      // 更新文件列表
      homeLogoFileList.value = [...info.fileList];
      // 如果有新文件，保存File对象
      if (info.fileList.length > 0) {
        const latestFile = info.fileList[info.fileList.length - 1];
        if (latestFile.originFileObj) {
          homeLogoFile.value = latestFile.originFileObj;
        }
      } else {
        homeLogoFile.value = null;
      }

      break;
    }
    case 'mobileLogo': {
      // 更新文件列表
      mobileLogoFileList.value = [...info.fileList];
      // 如果有新文件，保存File对象
      if (info.fileList.length > 0) {
        const latestFile = info.fileList[info.fileList.length - 1];
        if (latestFile.originFileObj) {
          mobileLogoFile.value = latestFile.originFileObj;
        }
      } else {
        mobileLogoFile.value = null;
      }

      break;
    }
    case 'tenantLogo': {
      // 更新文件列表
      logoFileList.value = [...info.fileList];
      // 如果有新文件，保存File对象
      if (info.fileList.length > 0) {
        const latestFile = info.fileList[info.fileList.length - 1];
        if (latestFile.originFileObj) {
          logoFile.value = latestFile.originFileObj;
        }
      } else {
        logoFile.value = null;
      }

      break;
    }
    // No default
  }
}

// 设置Logo预览
async function setupLogoPreview(
  logoType: 'homeLogo' | 'mobileLogo' | 'tenantLogo',
  logoUrl: string,
) {
  try {
    if (!logoUrl) {
      return;
    }

    // 设置预览文件列表
    const previewFile = {
      uid: `-${logoType}`,
      name: `${logoType}.png`,
      status: 'done' as const,
      url: logoUrl,
      thumbUrl: logoUrl,
      response: { url: logoUrl },
    };

    switch (logoType) {
      case 'homeLogo': {
        // 清空文件对象，因为这是预览已有的图片
        homeLogoFile.value = null;
        homeLogoFileList.value = [previewFile];
        // 直接设置表单字段的值为文件列表
        await formApi.setFieldValue('homeLogoUpload', homeLogoFileList.value);

        break;
      }
      case 'mobileLogo': {
        // 清空文件对象，因为这是预览已有的图片
        mobileLogoFile.value = null;
        mobileLogoFileList.value = [previewFile];
        // 直接设置表单字段的值为文件列表
        await formApi.setFieldValue(
          'mobileLogoUpload',
          mobileLogoFileList.value,
        );

        break;
      }
      case 'tenantLogo': {
        // 清空文件对象，因为这是预览已有的图片
        logoFile.value = null;
        logoFileList.value = [previewFile];
        // 直接设置表单字段的值为文件列表
        await formApi.setFieldValue('tenantLogoUpload', logoFileList.value);

        break;
      }
      // No default
    }

    // 等待一下确保状态更新
    await new Promise((resolve) => setTimeout(resolve, 200));

    // 再次强制更新组件配置
    await updateFormComponents();
  } catch (error) {
    console.error(`设置${logoType}预览失败:`, error);
  }
}

async function setupPackageSelect() {
  const tenantPackageList = await packageSelectList();
  const options = tenantPackageList.map((item) => ({
    label: item.packageName,
    value: item.packageId,
  }));
  formApi.updateSchema([
    {
      componentProps: {
        optionFilterProp: 'label',
        optionLabelProp: 'label',
        options,
        showSearch: true,
      },
      fieldName: 'packageId',
    },
  ]);
}

const tenantStore = useTenantStore();
async function handleConfirm() {
  try {
    drawerApi.drawerLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());

    // 移除Upload组件的中间字段
    delete data.tenantLogoUpload;
    delete data.homeLogoUpload;
    delete data.mobileLogoUpload;

    // 创建FormData对象
    const formData = new FormData();

    // 将业务数据包装到bo对象中
    const boData: Record<string, any> = { ...data };

    // 如果没有新文件上传且是编辑模式，保留原有logo URL
    if (!logoFile.value && isUpdate.value && data.tenantLogo) {
      boData.tenantLogo = data.tenantLogo;
    }
    if (!homeLogoFile.value && isUpdate.value && data.homeLogo) {
      boData.homeLogo = data.homeLogo;
    }
    if (!mobileLogoFile.value && isUpdate.value && data.mobileLogo) {
      boData.mobileLogo = data.mobileLogo;
    }

    // 将bo对象转为JSON字符串并添加到FormData
    formData.append(
      'bo',
      new Blob([JSON.stringify(boData)], { type: 'application/json' }),
    );

    // 如果有新文件，添加到FormData
    if (logoFile.value) {
      formData.append('tenantLogo', logoFile.value);
    }
    if (homeLogoFile.value) {
      formData.append('homeLogo', homeLogoFile.value);
    }
    if (mobileLogoFile.value) {
      formData.append('mobileLogo', mobileLogoFile.value);
    }

    // 提交表单数据
    await (isUpdate.value ? tenantUpdate(formData) : tenantAdd(formData));

    emit('reload');
    await handleCancel();
    // 重新加载租户信息
    tenantStore.initTenant();
  } catch (error) {
    console.error('提交表单错误:', error);
  } finally {
    drawerApi.drawerLoading(false);
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
  // 清理文件状态
  logoFileList.value = [];
  logoFile.value = null;
  homeLogoFileList.value = [];
  homeLogoFile.value = null;
  mobileLogoFileList.value = [];
  mobileLogoFile.value = null;
}
</script>

<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm />
  </BasicDrawer>
</template>

<style lang="scss" scoped>
:deep(.ant-divider) {
  margin: 8px 0;
}
</style>
