export default defineEventHandler(async () => {
  return useResponseSuccess({
    userCount: 1200,
    visitCount: 5000,
    downloadCount: 3500,
    usageCount: 2800,
    dailyVisitCount: 150,
    monthlyVisitCount: 4500,
    yearlyVisitCount: 54000,
    dailyLoginCount: 89,
    monthlyLoginCount: 2600,
    yearlyLoginCount: 31200,
    dailyPageCount: 320,
    monthlyPageCount: 9600,
    yearlyPageCount: 115200,
    dailyMessageCount: 45,
    monthlyMessageCount: 1350,
    yearlyMessageCount: 16200,
  });
});
