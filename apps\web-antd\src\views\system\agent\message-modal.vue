<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { MessageForm } from '#/api/system/agent/model';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Form, FormItem, Input, Textarea } from 'ant-design-vue';
import { pick } from 'lodash-es';

import { messageInfo } from '#/api/system/agent';
import { Tinymce } from '#/components/tinymce';

const title = computed(() => {
  return '查看智能体消息详情';
});

/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<MessageForm> = {
  id: undefined,
  userId: undefined,
  content: undefined,
  role: undefined,
  deductCost: undefined,
  totalTokens: undefined,
  modelName: undefined,
  remark: undefined,
  agentName: undefined,
  deductPower: undefined,
};

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

/**
 * useForm解构出表单方法
 */
const { resetFields } = Form.useForm(formData);

const [BasicModal, modalApi] = useVbenModal({
  class: 'w-[550px]',
  fullscreenButton: false,
  closeOnClickModal: false,
  onClosed: handleCancel,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);

    const { id } = modalApi.getData() as { id?: number | string };

    if (id) {
      const record = await messageInfo(id);
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
    }

    modalApi.modalLoading(false);
  },
});

async function handleCancel() {
  modalApi.close();
  formData.value = defaultValues;
  resetFields();
}
</script>

<template>
  <BasicModal :title="title" :show-submit-button="false">
    <Form :label-col="{ span: 4 }">
      <FormItem label="用户id">
        <Input v-model:value="formData.userId" readonly />
      </FormItem>
      <FormItem label="消息内容">
        <Tinymce :options="{ readonly: true }" v-model="formData.content" />
      </FormItem>
      <FormItem label="对话角色">
        <Input v-model:value="formData.role" readonly />
      </FormItem>
      <FormItem label="扣除金额">
        <Input v-model:value="formData.deductCost" readonly />
      </FormItem>
      <FormItem label="扣除算力">
        <Input v-model:value="formData.deductPower" readonly />
      </FormItem>
      <FormItem label="累计 Tokens">
        <Input v-model:value="formData.totalTokens" readonly />
      </FormItem>
      <FormItem label="智能体名称">
        <Input v-model:value="formData.agentName" readonly />
      </FormItem>
      <FormItem label="模型名称">
        <Input v-model:value="formData.modelName" readonly />
      </FormItem>
      <FormItem label="备注">
        <Textarea v-model:value="formData.remark" readonly :rows="4" />
      </FormItem>
    </Form>
  </BasicModal>
</template>
