<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';
import type { TabOption } from '@vben/types';

import { onMounted, ref } from 'vue';

import { AnalysisChartsTabs, AnalysisOverview } from '@vben/common-ui';
import {
  SvgBellIcon,
  SvgCakeIcon,
  SvgCardIcon,
  SvgDownloadIcon,
} from '@vben/icons';

import { messageList } from '#/api/analytics';

import AnalyticsVisits from './analytics-visits.vue';

// 响应式数据
const overviewItems = ref<AnalysisOverviewItem[]>([
  {
    icon: SvgCardIcon,
    title: '用户量',
    totalTitle: '总用户量',
    totalValue: 120_000,
    value: 2000,
  },
  {
    icon: SvgCakeIcon,
    title: '访问量',
    totalTitle: '总访问量',
    totalValue: 500_000,
    value: 20_000,
  },
  {
    icon: SvgDownloadIcon,
    title: '下载量',
    totalTitle: '总下载量',
    totalValue: 120_000,
    value: 80_000,
  },
  {
    icon: SvgBellIcon,
    title: '使用量',
    totalTitle: '总使用量',
    totalValue: 50_000,
    value: 5000,
  },
]);

// 加载数据
const loadAnalyticsData = async () => {
  try {
    const data = await messageList();
    console.log('Analytics data:', data);

    // 更新概览数据
    overviewItems.value = [
      {
        icon: SvgCardIcon,
        title: '用户量',
        totalTitle: '总用户量',
        totalValue: data.userCount || 0,
        value: data.dailyLoginCount || 0,
      },
      {
        icon: SvgCakeIcon,
        title: '访问量',
        totalTitle: '总访问量',
        totalValue: data.visitCount || 0,
        value: data.dailyVisitCount || 0,
      },
      {
        icon: SvgDownloadIcon,
        title: '下载量',
        totalTitle: '总下载量',
        totalValue: data.downloadCount || 0,
        value: data.dailyPageCount || 0,
      },
      {
        icon: SvgBellIcon,
        title: '使用量',
        totalTitle: '总使用量',
        totalValue: data.usageCount || 0,
        value: data.dailyMessageCount || 0,
      },
    ];
  } catch (error) {
    console.error('Failed to load analytics data:', error);
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadAnalyticsData();
});

const chartTabs: TabOption[] = [
  {
    label: '日访问量',
    value: 'visits',
  },
];
</script>

<template>
  <div class="p-5">
    <AnalysisOverview :items="overviewItems" />
    <AnalysisChartsTabs :tabs="chartTabs" class="mt-5">
      <template #visits>
        <AnalyticsVisits />
      </template>
    </AnalysisChartsTabs>

    <div class="mt-5 w-full md:flex">
      <!-- <AnalysisChartCard class="mt-5 md:mr-4 md:mt-0 md:w-1/3" title="访问数量">
        <AnalyticsVisitsData />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mr-4 md:mt-0 md:w-1/3" title="访问来源">
        <AnalyticsVisitsSource />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mt-0 md:w-1/3" title="访问来源">
        <AnalyticsVisitsSales />
      </AnalysisChartCard> -->
    </div>
  </div>
</template>
